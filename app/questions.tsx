import { ContentRenderer } from '@/components/ContentRenderer';
import { ModernLoader } from '@/components/ModernLoader';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useBookmarks } from '@/hooks/useBookmarks';
import { useDataManager } from '@/hooks/useDataManager';
import { useProgress } from '@/hooks/useProgress';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useMemo, useState } from 'react';
import {
  Alert,
  Animated,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import {
  Button,
  Text
} from 'react-native-paper';

// Theme hook



export default function QuestionsScreen() {
  const params = useLocalSearchParams();
  const { title, fileUrl } = params;
  // Get theme colors
  const primaryColor = useThemeColor({}, 'primary');
  const surfaceColor = useThemeColor({}, 'surface');
  const backgroundColor = useThemeColor({}, 'background');
  const shadowColor = useThemeColor({}, 'shadow');
  const successColor = useThemeColor({}, 'success');
  const errorColor = useThemeColor({}, 'error');
  const borderColor = useThemeColor({}, 'border');

  // Use DataManager for offline-first question loading
  const { loadQuestions, questions, loadingQuestions, errorQuestions } = useDataManager();

  // Use Bookmarks for bookmark functionality
  const { toggleBookmark, isBookmarked } = useBookmarks();

  // Use Progress for resume functionality
  const { saveProgress, getProgress, markQuestionCompleted, clearProgress } = useProgress();

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [showAnswer, setShowAnswer] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [completedQuestions, setCompletedQuestions] = useState<number[]>([]);

  // Animation states
  const [isAnimating, setIsAnimating] = useState(false);
  const slideAnim = useState(new Animated.Value(0))[0];
  const fadeAnim = useState(new Animated.Value(1))[0];

  // Filter states
  const [selectedSubject, setSelectedSubject] = useState<string>('All');
  const [showFilterModal, setShowFilterModal] = useState(false);

  useEffect(() => {
    if (fileUrl) {
      console.log('🔍 Loading questions from:', fileUrl);
      loadQuestions(fileUrl as string);
    }
  }, [fileUrl, loadQuestions]);

  // Load progress when questions are loaded
  useEffect(() => {
    const loadProgress = async () => {
      if (fileUrl && questions.length > 0) {
        try {
          const progress = await getProgress(fileUrl as string);
          if (progress) {
            console.log('📖 Resuming from progress:', progress);
            setCurrentQuestionIndex(progress.currentQuestionIndex);
            setSelectedSubject(progress.selectedSubject);
            setCompletedQuestions(progress.completedQuestions);
          }
        } catch (error) {
          console.error('Error loading progress:', error);
        }
      }
    };

    loadProgress();
  }, [fileUrl, questions.length, getProgress]);



  // Get unique subjects from questions
  const availableSubjects = useMemo(() => {
    if (!questions || questions.length === 0) return ['All'];
    const subjects = [...new Set(questions.map(q => q.subject))].filter(Boolean);
    return ['All', ...subjects.sort()];
  }, [questions]);

  // Filter questions by selected subject
  const filteredQuestions = useMemo(() => {
    if (!questions) return [];
    if (selectedSubject === 'All') return questions;
    return questions.filter(q => q.subject === selectedSubject);
  }, [questions, selectedSubject]);



  const currentQuestion = filteredQuestions[currentQuestionIndex];

  const handleAnswerSelect = async (answer: string) => {
    if (showAnswer) return; // Prevent changing selection after answer is shown
    setSelectedAnswer(answer);
    setShowAnswer(true); // Show correct/incorrect immediately when user selects

    // Mark question as completed
    if (!completedQuestions.includes(currentQuestionIndex)) {
      const newCompletedQuestions = [...completedQuestions, currentQuestionIndex];
      setCompletedQuestions(newCompletedQuestions);

      // Also mark in progress manager
      if (fileUrl) {
        try {
          await markQuestionCompleted(fileUrl as string, currentQuestionIndex);
        } catch (error) {
          console.error('Error marking question as completed:', error);
        }
      }
    }
  };


  // Use Interstitial Ad Hook
  const { isLoaded: adLoaded, showAd } = require('@/components/ads/InterstitialAd').useInterstitialAd();






  const handleNextQuestion = () => {
    if (currentQuestionIndex < filteredQuestions.length - 1) {
      if (isAnimating) return; // Prevent multiple animations

      setIsAnimating(true);
      const nextIndex = currentQuestionIndex + 1;

      // Animate current question out
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -300, // Slide current question to the left
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0, // Fade out current question
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Update states
        setCurrentQuestionIndex(nextIndex);
        setSelectedAnswer('');
        setShowAnswer(false);
        setShowExplanation(false);

        // Reset animation values for new question
        slideAnim.setValue(300); // Start new question from the right
        fadeAnim.setValue(0);

        // Animate new question in
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: 0, // Slide new question to center
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1, // Fade in new question
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          setIsAnimating(false);

          // Save progress
          if (fileUrl && title) {
            saveProgress(
              fileUrl as string,
              title as string,
              nextIndex,
              filteredQuestions.length,
              selectedSubject,
              completedQuestions
            ).catch((error) => {
              console.error('Error saving progress:', error);
            });
          }

          // Show interstitial ad every 5 questions
          if ((nextIndex + 1) % 5 === 0) {
            console.log(`Attempting to show interstitial ad at question ${nextIndex + 1}`);
            showAd();
          }
        });
      });
    } else {
      Alert.alert('Quiz Complete', `You have completed all ${filteredQuestions.length} questions!`);
    }
  };





  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      if (isAnimating) return; // Prevent multiple animations

      setIsAnimating(true);
      const prevIndex = currentQuestionIndex - 1;

      // Animate current question out
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 300, // Slide current question to the right
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0, // Fade out current question
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Update states
        setCurrentQuestionIndex(prevIndex);
        setSelectedAnswer('');
        setShowAnswer(false);
        setShowExplanation(false);

        // Reset animation values for previous question
        slideAnim.setValue(-300); // Start previous question from the left
        fadeAnim.setValue(0);

        // Animate previous question in
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: 0, // Slide previous question to center
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1, // Fade in previous question
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          setIsAnimating(false);

          // Save progress
          if (fileUrl && title) {
            saveProgress(
              fileUrl as string,
              title as string,
              prevIndex,
              filteredQuestions.length,
              selectedSubject,
              completedQuestions
            ).catch((error) => {
              console.error('Error saving progress:', error);
            });
          }
        });
      });
    }
  };

  const handleSubjectFilter = async (subject: string) => {
    setSelectedSubject(subject);
    setCurrentQuestionIndex(0); // Reset to first question when filter changes
    setSelectedAnswer('');
    setShowAnswer(false);
    setShowExplanation(false);
    setShowFilterModal(false);

    // Save progress with new subject filter
    if (fileUrl && title) {
      try {
        await saveProgress(
          fileUrl as string,
          title as string,
          0, // Reset to first question
          filteredQuestions.length,
          subject,
          completedQuestions
        );
      } catch (error) {
        console.error('Error saving progress after filter change:', error);
      }
    }
  };

  const handleBookmarkToggle = async () => {
    if (!currentQuestion) return;

    console.log('🎯 Questions screen - handleBookmarkToggle called:', {
      currentQuestion: currentQuestion.question_number,
      title: title as string,
      fileUrl: fileUrl as string
    });

    try {
      await toggleBookmark(currentQuestion, title as string, fileUrl as string);
      console.log('✅ Questions screen - bookmark toggle completed');
    } catch (error) {
      console.error('❌ Questions screen - bookmark toggle failed:', error);
      Alert.alert('Error', 'Failed to update bookmark');
    }
  };

  const handleResetProgress = () => {
    Alert.alert(
      'Reset Progress',
      'Are you sure you want to reset your progress for this question set? This will start from the beginning.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              if (fileUrl) {
                await clearProgress(fileUrl as string);
                setCurrentQuestionIndex(0);
                setCompletedQuestions([]);
                setSelectedAnswer('');
                setShowAnswer(false);
                setShowExplanation(false);
                console.log('✅ Progress reset successfully');
              }
            } catch (error) {
              console.error('❌ Error resetting progress:', error);
              Alert.alert('Error', 'Failed to reset progress');
            }
          }
        }
      ]
    );
  };

  if (loadingQuestions) {
    return (
      <ModernLoader
        message="Loading Questions"
        submessage={`Preparing ${title} questions for you...`}
      />
    );
  }

  if (errorQuestions) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText type="title">Error Loading Questions</ThemedText>
        <ThemedText style={{ marginTop: 10, textAlign: 'center' }}>
          {errorQuestions}
        </ThemedText>
        <Button onPress={() => router.back()} style={{ marginTop: 20 }}>
          Go Back
        </Button>
      </ThemedView>
    );
  }

  if (questions.length === 0) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText type="title">No Questions Found</ThemedText>
        <Button onPress={() => router.back()}>Go Back</Button>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <ThemedView style={[styles.header, { backgroundColor: primaryColor, shadowColor }]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol name="arrow.left" size={24} color="white" />
        </TouchableOpacity>
        <ThemedView style={[styles.headerCenter, { backgroundColor: 'transparent' }]}>
          <ThemedText style={[styles.headerTitle, { color: 'white' }]}>
            {title}
          </ThemedText>
        </ThemedView>
        <ThemedView style={[styles.headerActions, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity onPress={handleBookmarkToggle} style={styles.bookmarkButton}>
            <IconSymbol
              name={currentQuestion && isBookmarked(currentQuestion, fileUrl as string) ? 'bookmark.fill' : 'bookmark'}
              size={24}
              color="white"
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setShowFilterModal(true)}
            onLongPress={handleResetProgress}
            style={styles.filterButton}
          >
            <IconSymbol name="line.3.horizontal.decrease" size={24} color="white" />
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>

      <ScrollView style={[styles.content, { backgroundColor }]}>
        <Animated.View
          style={[
            styles.questionCard,
            {
              backgroundColor: surfaceColor,
              shadowColor,
              transform: [{ translateX: slideAnim }],
              opacity: fadeAnim
            }
          ]}
        >
          <ThemedView style={styles.cardContent}>
            <ThemedView style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <ThemedView style={{ flexDirection: 'row', alignItems: 'center' }}>
                <ThemedText type="subheading" style={[styles.questionNumber, { color: primaryColor }]}>
                  Question {currentQuestion.question_number}
                </ThemedText>
              </ThemedView>
              <ThemedView style={{ alignItems: 'flex-end' }}>
                <ThemedText style={[styles.questionNumber, { color: 'gray' }]}>
                  {currentQuestionIndex + 1}/{filteredQuestions.length}
                  {selectedSubject !== 'All' && ` • ${selectedSubject}`}
                </ThemedText>
              </ThemedView>
            </ThemedView>
            <ThemedView style={styles.questionTextContainer}>
              <ContentRenderer
                content={currentQuestion.question_text}
                style={styles.questionText}
              />
            </ThemedView>


            <ThemedView style={styles.optionsContainer}>
              {Object.entries(currentQuestion.options).map(([key, value]) => (
                <TouchableOpacity
                  key={key}
                  style={[
                    styles.optionItem,
                    { backgroundColor: surfaceColor, borderColor: borderColor, shadowColor: borderColor },
                    selectedAnswer === key && { backgroundColor: primaryColor + '15', borderColor: primaryColor, shadowColor: primaryColor },
                    showAnswer && key === currentQuestion.correct_answer && { backgroundColor: successColor + '15', borderColor: successColor, shadowColor: successColor },
                    showAnswer && selectedAnswer === key && key !== currentQuestion.correct_answer && { backgroundColor: errorColor + '15', borderColor: errorColor, shadowColor: errorColor }
                  ]}
                  onPress={() => handleAnswerSelect(key)}
                  disabled={showAnswer}
                >
                  <ThemedView style={styles.optionTextContainer}>
                    <ThemedText style={[
                      styles.optionLabel,
                      {
                        color: selectedAnswer === key ? 'white' : primaryColor,
                        backgroundColor: selectedAnswer === key ? primaryColor : surfaceColor,
                        borderColor: primaryColor,
                        borderWidth: selectedAnswer === key ? 0 : 1
                      }
                    ]}>{key}</ThemedText>
                    <ContentRenderer
                      content={value}
                      style={styles.optionText}
                    />
                  </ThemedView>
                </TouchableOpacity>
              ))}
            </ThemedView>

            {showAnswer && (
              <ThemedView style={[styles.answerContainer, { backgroundColor: surfaceColor, borderColor: borderColor }]}>
                {/* Show Explanation Button */}
                {currentQuestion.explanation && !showExplanation && (
                  <ThemedView style={styles.showExplanationContainer}>
                    <Button
                      mode="outlined"
                      onPress={() => setShowExplanation(true)}
                      style={styles.showExplanationButton}
                    >
                      Show Explanation
                    </Button>
                  </ThemedView>
                )}

                {/* Explanation Content */}
                {currentQuestion.explanation && showExplanation && (
                  <ThemedView style={[styles.explanationContainer, { backgroundColor: successColor + '10', borderColor: successColor + '30' }]}>
                    <ThemedText style={[styles.explanationLabel, { color: successColor }]}>Explanation: </ThemedText>
                    <ContentRenderer
                      content={currentQuestion.explanation}
                      style={styles.explanationText}
                    />
                  </ThemedView>
                )}
              </ThemedView>
            )}
          </ThemedView>
        </Animated.View>
      </ScrollView>

      {/* Bottom Navigation - Always visible */}
      <ThemedView style={[styles.bottomNavigation, { backgroundColor: surfaceColor, borderTopColor: borderColor }]}>
        <Button
          mode="outlined"
          onPress={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
          style={[styles.navButton, { borderColor: primaryColor }]}
          labelStyle={{ color: primaryColor }}
        >
          Previous
        </Button>
        <Button
          mode="contained"
          onPress={handleNextQuestion}
          style={[styles.navButton, { backgroundColor: primaryColor }]}
          labelStyle={{ color: 'white' }}
        >
          {currentQuestionIndex === filteredQuestions.length - 1 ? 'Finish' : 'Next'}
        </Button>
      </ThemedView>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter by Subject</Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.subjectList}>
              {availableSubjects.map((subject) => (
                <TouchableOpacity
                  key={subject}
                  style={[
                    styles.subjectItem,
                    selectedSubject === subject && styles.selectedSubjectItem
                  ]}
                  onPress={() => handleSubjectFilter(subject)}
                >
                  <Text style={[
                    styles.subjectText,
                    selectedSubject === subject && styles.selectedSubjectText
                  ]}>
                    {subject}
                  </Text>
                  {selectedSubject === subject && (
                    <Text style={styles.checkmark}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 20 : 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
  },
  backText: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 12,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 4,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bookmarkButton: {
    padding: 8,
    borderRadius: 8,
  },
  bookmarkText: {
    fontSize: 20,
  },
  filterButton: {
    padding: 8,
    borderRadius: 8,
  },
  filterText: {
    fontSize: 20,
  },
  content: {
    flex: 1,
    padding: 12,
  },
  questionCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  cardContent: {
    padding: 16,
  },
  questionNumber: {
    fontSize: 18,
    fontWeight: '800',
    marginBottom: 12,
    textAlign: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  bookmarkIndicator: {
    fontSize: 16,
    marginLeft: 8,
  },
  completedIndicator: {
    fontSize: 16,
    marginLeft: 8,
    fontWeight: '800',
  },
  questionTextContainer: {
    marginBottom: 16,
    padding: 2,
  },
  questionText: {
    fontSize: 18,
    lineHeight: 28,
    fontWeight: '500',
  },
  optionsContainer: {
    marginBottom: 16,
    gap: 8,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 2,
    minHeight: 60,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 4,
    // elevation: 2,
    transform: [{ scale: 1 }],
  },
  selectedOption: {
    borderWidth: 2,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 6,
    transform: [{ scale: 1.02 }],
  },
  correctOption: {
    borderWidth: 2,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 6,
  },
  wrongOption: {
    borderWidth: 2,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 6,
  },
  optionTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '800',
    minWidth: 28,
    height: 28,
    textAlign: 'center',
    lineHeight: 28,
    borderRadius: 14,
    marginRight: 10,
  },
  optionText: {
    flex: 1,
    fontSize: 17,
    lineHeight: 26,
    fontWeight: '500',
  },
  answerContainer: {
    padding: 14,
    backgroundColor: '#ecfdf5',
    borderRadius: 12,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#10b981',
    shadowColor: '#10b981',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  correctAnswerText: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 10,
  },
  showExplanationContainer: {
    marginTop: 12,
  },
  showExplanationButton: {
    borderRadius: 10,
  },
  explanationContainer: {
    marginTop: 12,
    padding: 16,
    borderRadius: 12,
    // borderWidth: 1,
  },
  explanationLabel: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
  showAnswerContainer: {
    marginBottom: 16,
    alignItems: 'center',
  },
  showAnswerButton: {
    marginVertical: 8,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
  },
  bottomNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 8,
  },
  navButton: {
    flex: 0.45,
    borderRadius: 12,
    paddingVertical: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  closeButton: {
    fontSize: 20,
    color: '#6b7280',
    fontWeight: '600',
  },
  subjectList: {
    maxHeight: 400,
  },
  subjectItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  selectedSubjectItem: {
    backgroundColor: '#f0f4ff',
  },
  subjectText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  selectedSubjectText: {
    color: '#667eea',
    fontWeight: '600',
  },
  checkmark: {
    fontSize: 16,
    color: '#667eea',
    fontWeight: '600',
  },
});
