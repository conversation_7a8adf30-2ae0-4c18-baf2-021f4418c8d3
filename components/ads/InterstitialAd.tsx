// import { useEffect, useState } from 'react';

// export const useInterstitialAd = () => {
//   const [isLoaded, setIsLoaded] = useState(false);
//   const [isLoading, setIsLoading] = useState(false);
//   const [interstitial, setInterstitial] = useState<any>(null);

//   useEffect(() => {
//     try {
//       const { AdEventType, InterstitialAd, TestIds } = require('react-native-google-mobile-ads');

//       if (!InterstitialAd) {
//         console.error('InterstitialAd not available');
//         return;
//       }

//       // Use test ad unit ID for development, replace with your actual ad unit ID for production
//       const adUnitId = __DEV__ ? TestIds.INTERSTITIAL : 'ca-app-pub-4790670410909128/7077701782';

//       const interstitialAd = InterstitialAd.createForAdUnitId(adUnitId, {
//         requestNonPersonalizedAdsOnly: true,
//       });

//       setInterstitial(interstitialAd);

//       const unsubscribeLoaded = interstitialAd.addAdEventListener(AdEventType.LOADED, () => {
//         setIsLoaded(true);
//         setIsLoading(false);
//         console.log('Interstitial ad loaded');
//       });

//       const unsubscribeError = interstitialAd.addAdEventListener(AdEventType.ERROR, (error: any) => {
//         setIsLoading(false);
//         console.log('Interstitial ad error:', error);
//       });

//       const unsubscribeClosed = interstitialAd.addAdEventListener(AdEventType.CLOSED, () => {
//         setIsLoaded(false);
//         console.log('Interstitial ad closed');
//         // Load a new ad for next time
//         loadAd();
//       });

//       // Load the initial ad
//       loadAd();

//       return () => {
//         unsubscribeLoaded();
//         unsubscribeError();
//         unsubscribeClosed();
//       };
//     } catch (error) {
//       console.error('Failed to initialize InterstitialAd:', error);
//     }
//   }, []);

//   const loadAd = () => {
//     if (interstitial && !isLoading && !isLoaded) {
//       setIsLoading(true);
//       interstitial.load();
//     }
//   };

//   const showAd = () => {
//     if (interstitial && isLoaded) {
//       interstitial.show();
//     } else {
//       console.log('Interstitial ad not ready yet');
//       // Optionally load a new ad
//       loadAd();
//     }
//   };

//   return {
//     isLoaded,
//     isLoading,
//     showAd,
//     loadAd,
//   };
// };
