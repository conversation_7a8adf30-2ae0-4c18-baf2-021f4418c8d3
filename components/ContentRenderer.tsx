import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import { WebView } from 'react-native-webview';

interface ContentRendererProps {
  content: string;
  style?: any;
}

export const ContentRenderer: React.FC<ContentRendererProps> = ({ content, style }) => {
  // Get theme colors
  const textColor = useThemeColor({}, 'text');
  // Check if content contains MathJax (looking for \\( \\) or \\[ \\] patterns)
  const hasMathJax = content.includes('\\(') || content.includes('\\[') || content.includes('$$') || content.includes('$');

  // Check if content contains image URLs
  const imageUrlRegex = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|bmp|webp))/gi;
  const imageUrls = content.match(imageUrlRegex) || [];

  // If content has both MathJax and images, or just MathJax, use WebView
  if (hasMathJax || imageUrls.length > 0) {
    return <MathJaxRenderer content={content} style={style} />;
  }

  // Plain text rendering
  return (
    <Text style={[styles.plainText, { color: textColor }, style]}>
      {content}
    </Text>
  );
};

// Component for rendering MathJax content with WebView
const MathJaxRenderer: React.FC<{ content: string; style?: any }> = ({ content, style }) => {
  // Get theme colors for WebView
  const colorScheme = useColorScheme();
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');

  // Use existing theme colors
  const isDark = colorScheme === 'dark';
  const cssTextColor = textColor;
  const cssBackgroundColor = backgroundColor;
  // Convert image URLs to HTML img tags
  const convertImagesToHtml = (text: string): string => {
    // More comprehensive regex to handle URLs with encoded characters
    const imageUrlRegex = /(https?:\/\/[^\s<>"']+\.(jpg|jpeg|png|gif|bmp|webp)(?:\?[^\s<>"']*)?)/gi;
    return text.replace(imageUrlRegex, (match) => {
      // Clean up the URL and ensure it's properly formatted
      const cleanUrl = match.trim();
      // Create a placeholder for offline images
      const placeholderSvg = `data:image/svg+xml;base64,${btoa(`
        <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ddd" stroke-width="2"/>
          <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="14" fill="#666">
            📷 Image unavailable offline
          </text>
        </svg>
      `)}`;

      return `<img src="${cleanUrl}" alt="Question Image"
                   style="max-width: 100%; height: auto; display: block; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                   onload="this.style.opacity=1;"
                   onerror="this.src='${placeholderSvg}'; this.alt='Image unavailable offline'; console.log('Image failed to load offline: ${cleanUrl}');" />`;
    });
  };

  // Convert content to HTML with MathJax support and proper image handling
  const processedContent = convertImagesToHtml(content);



  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
      <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
      <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
      <script>
        window.MathJax = {
          tex: {
            inlineMath: [['\\\\(', '\\\\)'], ['$', '$']],
            displayMath: [['\\\\[', '\\\\]'], ['$$', '$$']]
          },
          chtml: {
            scale: 1.0,
            minScale: 0.5,
            matchFontHeight: false
          },
          startup: {
            ready: () => {
              MathJax.startup.defaultReady();
              // Send height to React Native after rendering
              setTimeout(() => {
                const height = document.body.scrollHeight;
                window.ReactNativeWebView?.postMessage(JSON.stringify({type: 'height', height}));
              }, 500); // Increased timeout to allow images to load
            }
          }
        };

        // Handle image load events to recalculate height
        document.addEventListener('DOMContentLoaded', function() {
          const images = document.querySelectorAll('img');
          let loadedImages = 0;



          if (images.length === 0) {
            // No images, send height immediately after MathJax
            return;
          }

          images.forEach(function(img, index) {
            img.onload = function() {
              loadedImages++;
              if (loadedImages === images.length) {
                // All images loaded, recalculate height
                setTimeout(() => {
                  const height = document.body.scrollHeight;
                  window.ReactNativeWebView?.postMessage(JSON.stringify({type: 'height', height}));
                }, 100);
              }
            };

            img.onerror = function() {
              loadedImages++;
              if (loadedImages === images.length) {
                // All images processed (loaded or failed), recalculate height
                setTimeout(() => {
                  const height = document.body.scrollHeight;
                  window.ReactNativeWebView?.postMessage(JSON.stringify({type: 'height', height}));
                }, 100);
              }
            };

            // If image is already loaded (cached)
            if (img.complete) {
              loadedImages++;
              if (loadedImages === images.length) {
                setTimeout(() => {
                  const height = document.body.scrollHeight;
                  window.ReactNativeWebView?.postMessage(JSON.stringify({type: 'height', height}));
                }, 100);
              }
            }
          });
        });
      </script>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 20px;
          line-height: 2;
          margin: 0;
          // padding: 8px;
          // background-color: ${cssBackgroundColor};
          color: ${cssTextColor};
        }
        img {
          max-width: 100%;
          min-width: 100%;
          height: auto;
          display: block;
          margin: 10px 0;
          padding-bottom: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 8px ${isDark ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.1)'};
          ${isDark ? 'filter: brightness(0.9);' : ''}
        }
        .math {
          overflow-x: auto;
        }
        p {
          margin: 8px 0;
        }
      </style>
    </head>
    <body>
      ${processedContent}
    </body>
    </html>
  `;

  const [webViewHeight, setWebViewHeight] = React.useState(100);

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'height') {
        setWebViewHeight(Math.max(data.height, 50));
      }
    } catch (error) {
      // Ignore parsing errors
      console.warn('Error parsing WebView message:', error);
    }
  };

  return (
    <View style={[styles.webViewContainer, style, { height: webViewHeight }]}>
      <WebView
        source={{ html: htmlContent }}
        style={styles.webView}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        onMessage={handleMessage}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={false}
        scalesPageToFit={false}
        mixedContentMode="compatibility"
        originWhitelist={['*']}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  plainText: {
    fontSize: 18,
    lineHeight: 22,
    color: '#333',
    backgroundColor: 'transparent',
  },
  webViewContainer: {
    minHeight: 30,
    backgroundColor: 'transparent',
  },
  webView: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});